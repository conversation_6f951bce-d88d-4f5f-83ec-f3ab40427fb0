/* === موتور تاریخ جلالی (JDN<-><PERSON><PERSON><PERSON>) === */
const J<PERSON><PERSON> = (() => {
  function div(a,b){return ~~(a/b)}; 
  function mod(a,b){return a-b*div(a,b)}
  function jalCal(jy){
    const breaks=[-61,9,38,199,426,686,756,818,1111,1181,1210,1635,2060,2097,2192,2262,2324,2394,2456,3178];
    let bl=breaks.length, gy=jy+621, leapJ=-14, jp=breaks[0], jm,jump,leap,n,i;
    if(jy<jp||jy>=breaks[bl-1]) throw new Error('jalCal: out of range');
    for(i=1;i<bl;i++){ jm=breaks[i]; jump=jm-jp; if(jy<jm) break; leapJ+=div(jump,33)*8+div(mod(jump,33),4); jp=jm; }
    n=jy-jp; leapJ+=div(n,33)*8+div(mod(n,33)+3,4); if(mod(jump,33)===4 && jump-n===4) leapJ++;
    let leapG=div(gy,4)-div((div(gy,100)+1)*3,4)-150; let march=20+leapJ-leapG; 
    if(jump-n<6) n=n-jump+div(jump+4,33)*33; leap=mod(mod(n+1,33)-1,4); if(leap===-1) leap=4; 
    return {leap:leap===0, gy, march};
  }
  function g2d(gy,gm,gd){ let a=div(14-gm,12), y=gy+4800-a, m=gm+12*a-3; return gd+div(153*m+2,5)+365*y+div(y,4)-div(y,100)+div(y,400)-32045 }
  function d2g(jdn){ let j=jdn+32044, g=div(j,146097), dg=mod(j,146097), c=div((div(dg,36524)+1)*3,4), dc=dg-c*36524, b=div(dc,1461), db=mod(dc,1461), a=div((div(db,365)+1)*3,4), da=db-a*365, y=g*400+c*100+b*4+a, m=div(5*da+308,153)-2, d=da-div(153*m+2,5)+1; y=y-4800+div(m+2,12); m=mod(m+2,12)+1; return {gy:y,gm:m,gd:d} }
  function j2d(jy,jm,jd){ const r=jalCal(jy); return g2d(r.gy,3,r.march)+(jm-1)*31-div(jm,7)*(jm-7)+jd-1 }
  function d2j(jdn){ const g=d2g(jdn); let jy=g.gy-621; const r=jalCal(jy); let jdn1=g2d(r.gy,3,r.march); let k=jdn-jdn1; if(k>=0){ if(k<=185){ const jm=1+div(k,31); const jd=mod(k,31)+1; return {jy,jm,jd} } k-=186; return {jy, jm:7+div(k,30), jd:mod(k,30)+1} } jy-=1; const r2=jalCal(jy); jdn1=g2d(r2.gy,3,r2.march); k=jdn-jdn1; if(k<=185){ return {jy, jm:1+div(k,31), jd:mod(k,31)+1 } } k-=186; return {jy, jm:7+div(k,30), jd:mod(k,30)+1} }
  function toJ(gy,gm,gd){return d2j(g2d(gy,gm,gd))} 
  function toG(jy,jm,jd){return d2g(j2d(jy,jm,jd))}
  function monthLength(jy,jm){return jm<=6?31:(jm<=11?30:(isLeap(jy)?30:29))} 
  function isLeap(jy){return jalCal(jy).leap}
  return {toJ,toG,monthLength,isLeap,j2d,d2j};
})();

const WEEKDAYS = ["شنبه","یکشنبه","دوشنبه","سه‌شنبه","چهارشنبه","پنجشنبه","جمعه"];
const MONTHS = ["فروردین","اردیبهشت","خرداد","تیر","مرداد","شهریور","مهر","آبان","آذر","دی","بهمن","اسفند"];

const state={view:'month', focus:null, selected:null, editing:null};
(function init(){ const n=new Date(); const j=Jalaali.toJ(n.getFullYear(),n.getMonth()+1,n.getDate()); state.focus={...j}; state.selected={...j}; })();
/* === موتور تاریخ جلالی (JDN<->Jalali) === */
const Jalaali = (() => {
  function div(a,b){return ~~(a/b)}; 
  function mod(a,b){return a-b*div(a,b)}
  function jalCal(jy){
    const breaks=[-61,9,38,199,426,686,756,818,1111,1181,1210,1635,2060,2097,2192,2262,2324,2394,2456,3178];
    let bl=breaks.length, gy=jy+621, leapJ=-14, jp=breaks[0], jm,jump,leap,n,i;
    if(jy<jp||jy>=breaks[bl-1]) throw new Error('jalCal: out of range');
    for(i=1;i<bl;i++){ jm=breaks[i]; jump=jm-jp; if(jy<jm) break; leapJ+=div(jump,33)*8+div(mod(jump,33),4); jp=jm; }
    n=jy-jp; leapJ+=div(n,33)*8+div(mod(n,33)+3,4); if(mod(jump,33)===4 && jump-n===4) leapJ++;
    let leapG=div(gy,4)-div((div(gy,100)+1)*3,4)-150; let march=20+leapJ-leapG; 
    if(jump-n<6) n=n-jump+div(jump+4,33)*33; leap=mod(mod(n+1,33)-1,4); if(leap===-1) leap=4; 
    return {leap:leap===0, gy, march};
  }
  function g2d(gy,gm,gd){ let a=div(14-gm,12), y=gy+4800-a, m=gm+12*a-3; return gd+div(153*m+2,5)+365*y+div(y,4)-div(y,100)+div(y,400)-32045 }
  function d2g(jdn){ let j=jdn+32044, g=div(j,146097), dg=mod(j,146097), c=div((div(dg,36524)+1)*3,4), dc=dg-c*36524, b=div(dc,1461), db=mod(dc,1461), a=div((div(db,365)+1)*3,4), da=db-a*365, y=g*400+c*100+b*4+a, m=div(5*da+308,153)-2, d=da-div(153*m+2,5)+1; y=y-4800+div(m+2,12); m=mod(m+2,12)+1; return {gy:y,gm:m,gd:d} }
  function j2d(jy,jm,jd){ const r=jalCal(jy); return g2d(r.gy,3,r.march)+(jm-1)*31-div(jm,7)*(jm-7)+jd-1 }
  function d2j(jdn){ const g=d2g(jdn); let jy=g.gy-621; const r=jalCal(jy); let jdn1=g2d(r.gy,3,r.march); let k=jdn-jdn1; if(k>=0){ if(k<=185){ const jm=1+div(k,31); const jd=mod(k,31)+1; return {jy,jm,jd} } k-=186; return {jy, jm:7+div(k,30), jd:mod(k,30)+1} } jy-=1; const r2=jalCal(jy); jdn1=g2d(r2.gy,3,r2.march); k=jdn-jdn1; if(k<=185){ return {jy, jm:1+div(k,31), jd:mod(k,31)+1 } } k-=186; return {jy, jm:7+div(k,30), jd:mod(k,30)+1} }
  function toJ(gy,gm,gd){return d2j(g2d(gy,gm,gd))} 
  function toG(jy,jm,jd){return d2g(j2d(jy,jm,jd))}
  function monthLength(jy,jm){return jm<=6?31:(jm<=11?30:(isLeap(jy)?30:29))} 
  function isLeap(jy){return jalCal(jy).leap}
  return {toJ,toG,monthLength,isLeap,j2d,d2j};
})();

const WEEKDAYS = ["شنبه","یکشنبه","دوشنبه","سه‌شنبه","چهارشنبه","پنجشنبه","جمعه"];
const MONTHS = ["فروردین","اردیبهشت","خرداد","تیر","مرداد","شهریور","مهر","آبان","آذر","دی","بهمن","اسفند"];

const state={view:'month', focus:null, selected:null, editing:null};
(function init(){ const n=new Date(); const j=Jalaali.toJ(n.getFullYear(),n.getMonth()+1,n.getDate()); state.focus={...j}; state.selected={...j}; })();

function keyFrom(j){return `${j.jy}-${String(j.jm).padStart(2,'0')}-${String(j.jd).padStart(2,'0')}`}
function same(a,b){return a&&b&&a.jy===b.jy&&a.jm===b.jm&&a.jd===b.jd}
function todayJ(){ const d=new Date(); return Jalaali.toJ(d.getFullYear(),d.getMonth()+1,d.getDate()) }

// storage
function loadAll(){const r=localStorage.getItem('jalali.events'); return r?JSON.parse(r):{}}; 
function saveAll(o){localStorage.setItem('jalali.events',JSON.stringify(o))}
function listByDate(k){const a=loadAll(); return (a[k]||[]).sort((x,y)=> (x.time||'')>(y.time||'')?1:-1)}
function addEvent(k,ev){const a=loadAll(); a[k]=a[k]||[]; ev.id=ev.id||crypto.randomUUID(); a[k].push(ev); saveAll(a); return ev.id}
function updateEvent(ok,id,nk,ev){const a=loadAll(); if(!a[ok]) return; const i=a[ok].findIndex(x=>x.id===id); if(i>-1){ const item={...a[ok][i],...ev}; if(ok===nk) a[ok][i]=item; else { a[ok].splice(i,1); a[nk]=a[nk]||[]; a[nk].push(item);} saveAll(a);} }
function removeEvent(k,id){const a=loadAll(); if(!a[k]) return; a[k]=a[k].filter(x=>x.id!==id); saveAll(a)}

// DOM
const grid=document.getElementById('month-grid');
const weekGrid=document.getElementById('week-grid');
const dayList=document.get

function keyFrom(j){return `${j.jy}-${String(j.jm).padStart(2,'0')}-${String(j.jd).padStart(2,'0')}`}
function same(a,b){return a&&b&&a.jy===b.jy&&a.jm===b.jm&&a.jd===b.jd}
function todayJ(){ const d=new Date(); return Jalaali.toJ(d.getFullYear(),d.getMonth()+1,d.getDate()) }

// storage
function loadAll(){const r=localStorage.getItem('jalali.events'); return r?JSON.parse(r):{}}; 
function saveAll(o){localStorage.setItem('jalali.events',JSON.stringify(o))}
function listByDate(k){const a=loadAll(); return (a[k]||[]).sort((x,y)=> (x.time||'')>(y.time||'')?1:-1)}
function addEvent(k,ev){const a=loadAll(); a[k]=a[k]||[]; ev.id=ev.id||crypto.randomUUID(); a[k].push(ev); saveAll(a); return ev.id}
function updateEvent(ok,id,nk,ev){const a=loadAll(); if(!a[ok]) return; const i=a[ok].findIndex(x=>x.id===id); if(i>-1){ const item={...a[ok][i],...ev}; if(ok===nk) a[ok][i]=item; else { a[ok].splice(i,1); a[nk]=a[nk]||[]; a[nk].push(item);} saveAll(a);} }
function removeEvent(k,id){const a=loadAll(); if(!a[k]) return; a[k]=a[k].filter(x=>x.id!==id); saveAll(a)}

// DOM
const grid=document.getElementById('month-grid');
const weekGrid=document.getElementById('week-grid');
const dayList=document.get
