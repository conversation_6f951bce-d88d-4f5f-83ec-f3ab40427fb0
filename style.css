:root{
  --bg:#f8faf9;
  --text:#263238;
  --muted:#6b7b83;
  --brand:#0f7a6c;
  --brand-ink:#e7f3f1;
  --accent:#d26a12;
  --tile:#f2f4f5;
  --tile-alt:#ffffff;
  --border:#e7ecee;
  --shadow: 0 5px 20px rgba(0,0,0,.08);
  --radius: 18px;
}
*{box-sizing:border-box}
html,body{height:100%}
body{margin:0;font-family: Vazirmatn, IRANSans, Inter, system-ui, -apple-system, Segoe UI, Roboto, "Helvetica Neue", Arial; background:var(--bg); color:var(--text)}
.app{max-width:1000px;margin:24px auto;padding:0 16px}

/* HEADER BAR */
.bar{background:var(--brand); color:#fff; border-radius:28px; padding:10px 14px; display:flex; align-items:center; gap:10px; box-shadow:var(--shadow)}
.bar .spacer{flex:1}
.bar .btn{background:transparent;border:0;color:#fff;padding:10px 12px;border-radius:16px;cursor:pointer}
.bar .btn:hover{background:rgba(255,255,255,.12)}
.bar .drop{appearance:none;background:transparent;border:0;color:#fff;font-weight:800;font-size:20px;padding:6px 24px 6px 8px; border-radius:12px; cursor:pointer}
.bar .group{display:flex; align-items:center; gap:6px}

/* WEEKDAYS */
.weekdays{display:grid; grid-template-columns:repeat(7,1fr); margin:14px 8px 8px; color:#a75a2a; font-weight:800}

/* MONTH GRID */
.panel{background:#fff; border:1px solid var(--border); border-radius:var(--radius); box-shadow:var(--shadow)}
.month{padding:8px}
.grid{display:grid; grid-template-columns:repeat(7,1fr); gap:10px; padding:8px}

.cell{position:relative; min-height:88px; border-radius:18px; background:var(--tile); border:1px solid var(--border); padding:10px; display:flex; flex-direction:column; justify-content:flex-start; align-items:flex-start; gap:6px; transition:.15s transform}
.cell:hover{transform:translateY(-2px)}
.cell.other{background:var(--tile-alt); opacity:.75}

.num-fa{font-size:26px; font-weight:900; color:#3d4a4f}
.num-en{position:absolute; left:10px; bottom:8px; font-size:12px; color:#9aa9af}

.selected{outline:2px solid var(--accent); box-shadow:0 0 0 3px rgba(210,106,18,.15) inset}
.today{background:#fff3e8; border-color:#ffd9b8}
.fri .num-fa{color:#bf5b04}

.ev{display:flex; gap:4px; flex-wrap:wrap; margin-top:auto}
.ev span{font-size:11px; background:#eef5f4; color:#2f5d56; padding:3px 6px; border-radius:999px; border:1px solid #d8e9e6; max-width:100%; overflow:hidden; text-overflow:ellipsis; white-space:nowrap}

/* WEEK VIEW */
.week{padding:12px}
.week-grid{display:grid; grid-template-columns:120px repeat(7,1fr); gap:8px}
.hour{height:28px; font-size:12px; color:#7b8a90; text-align:center}
.col{background:var(--tile); border:1px solid var(--border); border-radius:14px; padding:8px; min-height:420px}

/* DAY VIEW */
.day{padding:16px; display:grid; grid-template-columns:1.1fr .9fr; gap:16px}
.big{font-weight:900; font-size:32px}
.list{display:flex; flex-direction:column; gap:10px}
.item{display:flex; gap:10px; background:#fff; border:1px solid var(--border); border-radius:14px; padding:10px}
.item .dot{width:10px;height:10px;border-radius:50%;background:var(--accent);margin-top:6px}
.del{margin-inline-start:auto; background:#fff; border:1px solid #ffd9b8; color:#bf5b04; border-radius:10px; padding:6px 10px}

/* MODAL */
.modal{position:fixed; inset:0; background:rgba(0,0,0,.35); display:none; align-items:center; justify-content:center; padding:16px}
.modal.show{display:flex}
.card{width:min(520px,100%); background:#fff; border:1px solid var(--border); border-radius:18px; padding:16px; box-shadow:var(--shadow)}
label{font-size:12px; color:#60727a}
input, select, textarea{background:#f7f9fa; border:1px solid var(--border); border-radius:12px; padding:10px}
.row{display:grid; grid-template-columns:1fr 1fr; gap:8px}

@media(max-width:900px){ 
  .day{grid-template-columns:1fr} 
  .week-grid{grid-template-columns:80px repeat(7,1fr)} 
}
